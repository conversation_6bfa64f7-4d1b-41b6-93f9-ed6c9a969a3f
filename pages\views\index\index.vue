<template>
	<view class="device-alert-app">
		<!-- 标题栏 -->
		<view class="header">
			<text class="title">设备告警</text>
			<!-- WebSocket 状态指示器 -->
			<view class="websocket-status" :class="websocketStatusClass">
				<text class="status-dot"></text>
				<text class="status-text">{{ websocketStatusText }}</text>
			</view>
			<view class="settings-btn" @tap="backIndex">
				<image src="./img/icon_02.png" mode=""></image>
			</view>
		</view>

		<!-- 告警横幅 -->
		<view class="alert-banner">
			<view class="alert-icon">
				<text class="alert-icon-text">⚠️</text>
			</view>
			<text class="alert-text">设备01在迁移至新疆机房过程中发生偏移</text>
		</view>

		<!-- 地图容器 -->
		<view class="map-container">
			<map id="map" class="map" :longitude="mapCenter.longitude" :latitude="mapCenter.latitude" :scale="12"
				:markers="markers" :circles="circles" :show-location="true" @markertap="onMarkerTap"
				@regionchange="onRegionChange" :enable-3D="true" :enable-rotate="true" :enable-overlooking="true"
				:enable-zoom="true" :enable-scroll="true" :enable-satellite="false" :enable-traffic="false"
				:map-style="mapStyle" @error="onMapError"></map>
		</view>

		<!-- 设备信息面板 -->
		<view class="device-info-panel">
			<view class="info-row">
				<text class="label">设备编号：</text>
				<text class="value">设备01</text>
			</view>
			<view class="info-row">
				<text class="label">设备类型：</text>
				<text class="value">路由器</text>
			</view>
			<view class="info-row">
				<text class="label">设备状态：</text>
				<text class="value status-migrating">迁移中</text>
			</view>
			<view class="info-row">
				<text class="label">设备迁移原因：</text>
				<text class="value">借出</text>
			</view>
			<view class="info-row">
				<text class="label">设备迁移人员：</text>
				<text class="value">张三</text>
			</view>
			<view class="info-row">
				<text class="label">设备迁移时间：</text>
				<text class="value">2025-05-28 15:00:00</text>
			</view>
			<view class="info-row">
				<text class="label">备注：</text>
				<text class="value note-text">小心破碎</text>
			</view>
		</view>
	</view>
</template>

<script>
	// import uni from 'uni-app'; // Declare the uni variable

	export default {
		data() {
			return {
				statusBarHeight: 0,
				// WebSocket 状态
				websocketConnected: false,
				websocketConnecting: false,
				mapCenter: {
					longitude: 87.6177, // 乌鲁木齐经度
					latitude: 43.7928 // 乌鲁木齐纬度
				},
				mapStyle: 'normal', // 地图样式：normal-标准，satellite-卫星
				// 地图标记点
				markers: [{
						id: 1,
						longitude: 87.6177,
						latitude: 43.7928,
						iconPath: '/static/images/marker-red.png',
						width: 30,
						height: 30,
						title: '设备01当前位置',
						callout: {
							content: '设备01',
							color: '#ffffff',
							fontSize: 12,
							borderRadius: 4,
							bgColor: '#ff4444',
							padding: 8,
							display: 'ALWAYS'
						}
					},
					{
						id: 2,
						longitude: 87.6277,
						latitude: 43.7728,
						iconPath: '/static/images/marker-green.png',
						width: 30,
						height: 30,
						title: '目标机房位置',
						callout: {
							content: '目标位置',
							color: '#ffffff',
							fontSize: 12,
							borderRadius: 4,
							bgColor: '#44ff44',
							padding: 8,
							display: 'ALWAYS'
						}
					}
				],
				// 地图圆圈
				circles: [{
						longitude: 87.6177,
						latitude: 43.7928,
						radius: 2000,
						strokeWidth: 3,
						color: '#ff444480',
						fillColor: '#ff444420'
					},
					{
						longitude: 87.6277,
						latitude: 43.7728,
						radius: 2000,
						strokeWidth: 3,
						color: '#44ff4480',
						fillColor: '#44ff4420'
					}
				]
			}
		},
		computed: {
			// WebSocket 状态样式类
			websocketStatusClass() {
				if (this.websocketConnecting) {
					return 'status-connecting';
				} else if (this.websocketConnected) {
					return 'status-connected';
				} else {
					return 'status-disconnected';
				}
			},
			// WebSocket 状态文本
			websocketStatusText() {
				if (this.websocketConnecting) {
					return '连接中';
				} else if (this.websocketConnected) {
					return '已连接';
				} else {
					return '未连接';
				}
			}
		},
		onLoad() {
			this.getSystemInfo()
			this.initMap()
			// 检查权限
			this.checkPermission()
			// 监控 WebSocket 状态
			this.monitorWebSocketStatus()
		},
		onReady() {
			// 页面渲染完成后，可以进行一些地图操作
			this.mapContext = uni.createMapContext('map', this)
		},
		methods: {
			// 获取系统信息
			getSystemInfo() {
				const systemInfo = uni.getSystemInfoSync()
				this.statusBarHeight = systemInfo.statusBarHeight || 20
			},
			backIndex() {
				uni.navigateTo({
					url: "/pages/views/index/CabinetDoorStatus"
				});
			},

			// 检查权限
			checkPermission() {
				// #ifdef APP-PLUS
				plus.android.requestPermissions(
					['android.permission.ACCESS_FINE_LOCATION', 'android.permission.ACCESS_COARSE_LOCATION'],
					function(resultObj) {
						if (resultObj.granted.length === 2) {
							console.log('位置权限已获取')
						} else {
							uni.showToast({
								title: '请授予位置权限',
								icon: 'none'
							})
						}
					},
					function(error) {
						console.error('申请权限错误：' + error.code + ' = ' + error.message)
					}
				)
				// #endif
			},

			// 地图错误处理
			onMapError(e) {
				console.error('地图错误：', e)
				uni.showToast({
					title: '地图加载失败，请检查网络和权限',
					icon: 'none'
				})
			},

			// 初始化地图
			initMap() {
				// #ifdef APP-PLUS
				plus.maps.Map.setAMapKey('2c8a4ecb1b19c0dcbe6bc794cabb37c1')
				// #endif
				console.log('地图初始化完成')
			},

			// 标记点击事件
			onMarkerTap(e) {
				const markerId = e.detail.markerId
				const marker = this.markers.find(m => m.id === markerId)
				if (marker) {
					uni.showToast({
						title: marker.title,
						icon: 'none',
						duration: 2000
					})
				}
			},

			// 地图区域变化事件
			onRegionChange(e) {
				console.log('地图区域变化:', e.detail)
			},

			// 刷新设备位置
			refreshDeviceLocation() {
				uni.showLoading({
					title: '刷新中...'
				})

				// 模拟获取新的设备位置
				setTimeout(() => {
					// 这里可以调用API获取最新的设备位置
					uni.hideLoading()
					uni.showToast({
						title: '位置已更新',
						icon: 'success'
					})
				}, 1500)
			},

			// 监控 WebSocket 状态
			monitorWebSocketStatus() {
				// 定时检查 WebSocket 状态
				setInterval(() => {
					const app = getApp();
					if (app && app.$socket) {
						const socket = app.$socket;
						if (socket.readyState === 1) {
							// 连接已打开
							this.websocketConnected = true;
							this.websocketConnecting = false;
						} else if (socket.readyState === 0) {
							// 正在连接
							this.websocketConnected = false;
							this.websocketConnecting = true;
						} else {
							// 连接已关闭或出错
							this.websocketConnected = false;
							this.websocketConnecting = false;
						}
					} else {
						// 没有 socket 实例
						this.websocketConnected = false;
						this.websocketConnecting = false;
					}
				}, 1000); // 每秒检查一次
			}
		}
	}
</script>

<style>
	page {
		height: 100%;
		background-color: #f5f5f5;
	}

	.device-alert-app {
		height: 100vh;
		display: flex;
		flex-direction: column;
		font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
	}

	/* 状态栏 */
	.status-bar {
		background: #4A90E2;
		color: white;
		padding: 8rpx 32rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;
		font-size: 28rpx;
		font-weight: 600;
	}

	.status-icons {
		display: flex;
		align-items: center;
		gap: 16rpx;
	}

	.signal,
	.wifi,
	.bluetooth {
		font-size: 24rpx;
	}

	.battery {
		font-size: 24rpx;
	}

	.battery-icon {
		font-size: 32rpx;
	}

	/* 标题栏 */
	.header {
		background: #4A90E2;
		color: white;
		padding: 32rpx;
		text-align: center;
		height: 80px;
		line-height: 80px;

		background-image: linear-gradient(89deg, #026BE7 0%, #2868EC 100%);
		background-image: url('./img/app-bg.png');
		background-repeat: no-repeat;
		background-size: cover;
	}

	.title {
		font-size: 18px;
		color: #FFFFFF;
		text-align: left;
		line-height: 24px;
		font-weight: 400;
	}

	/* WebSocket 状态指示器 */
	.websocket-status {
		position: absolute;
		top: 20rpx;
		right: 120rpx;
		display: flex;
		align-items: center;
		gap: 8rpx;
		padding: 8rpx 16rpx;
		border-radius: 20rpx;
		background: rgba(255, 255, 255, 0.2);
		backdrop-filter: blur(10rpx);
	}

	.status-dot {
		width: 12rpx;
		height: 12rpx;
		border-radius: 50%;
		display: block;
	}

	.status-text {
		font-size: 24rpx;
		color: #FFFFFF;
		font-weight: 500;
	}

	/* 不同状态的样式 */
	.status-connected .status-dot {
		background: #4CAF50;
		box-shadow: 0 0 8rpx rgba(76, 175, 80, 0.6);
	}

	.status-connecting .status-dot {
		background: #FF9800;
		animation: pulse 1.5s infinite;
	}

	.status-disconnected .status-dot {
		background: #F44336;
	}

	/* 脉冲动画 */
	@keyframes pulse {
		0% {
			transform: scale(1);
			opacity: 1;
		}
		50% {
			transform: scale(1.2);
			opacity: 0.7;
		}
		100% {
			transform: scale(1);
			opacity: 1;
		}
	}

	/* 告警横幅 */
	.alert-banner {
		background: linear-gradient(135deg, #ff6b6b, #ffa500);
		color: white;
		padding: 24rpx 32rpx;
		display: flex;
		align-items: center;
		gap: 24rpx;
		box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
		    /* background: rgba(209, 0, 0, 0.55); */
	}

	.alert-icon {
		background: rgba(255, 255, 255, 0.2);
		border-radius: 50%;
		width: 64rpx;
		height: 64rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.alert-icon-text {
		font-size: 40rpx;
	}

	.alert-text {
		font-size: 28rpx;
		font-weight: 500;
		flex: 1;
	}

	/* 地图容器 */
	.map-container {
		flex: 1;
		width: 100%;
		min-height: 600rpx;
	}

	.map {
		width: 100%;
		height: 100%;
	}

	/* 设备信息面板 */
	.device-info-panel {
		background: white;
		padding: 40rpx;
		border-top: 1rpx solid #e0e0e0;
		box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
	}

	.info-row {
		display: flex;
		margin-bottom: 24rpx;
		align-items: center;
	}

	.info-row:last-child {
		margin-bottom: 0;
	}

	.label {
		font-size: 28rpx;
		color: #666;
		min-width: 240rpx;
		font-weight: 500;
	}

	.value {
		font-size: 28rpx;
		color: #333;
		font-weight: 600;
		flex: 1;
	}

	.status-migrating {
		color: #ff6b6b;
	}

	.note-text {
		color: #ff9500;
	}

	/* 响应式适配 */
	@media (max-width: 750rpx) {
		.status-bar {
			padding: 6rpx 24rpx;
			font-size: 24rpx;
		}

		.header {
			padding: 24rpx;
		}

		.title {
			font-size: 32rpx;
		}

		.alert-banner {
			padding: 20rpx 24rpx;
		}

		.alert-text {
			font-size: 26rpx;
		}

		.device-info-panel {
			padding: 32rpx;
		}

		.label {
			font-size: 26rpx;
			min-width: 200rpx;
		}

		.value {
			font-size: 26rpx;
		}
	}

	/* 设置按钮 */
	.settings-btn {
		position: absolute;
		top: 64rpx;
		right: 30rpx;
		width: 80rpx;
		height: 80rpx;
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		transition: all 0.3s ease;
		z-index: 15;
	}

	.settings-btn image {
		width: 60rpx;
		height: 60rpx;
	}
</style>