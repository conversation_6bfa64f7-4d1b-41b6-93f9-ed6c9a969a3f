<script>
import Vue from 'vue'
import {
	mapMutations
} from 'vuex'
import {
	version
} from './package.json'
import checkUpdate from '@/uni_modules/uni-upgrade-center-app/utils/check-update';
// import 'https://cdn.socket.io/4.7.2/socket.io.min.js'
export default {
	onLaunch: function () {
		// #ifdef H5
		console.log(
			`%c hello uniapp %c v${version} `,
			'background:#35495e ; padding: 1px; border-radius: 3px 0 0 3px;  color: #fff',
			'background:#007aff ;padding: 1px; border-radius: 0 3px 3px 0;  color: #fff; font-weight: bold;'
		)
		// #endif
		// 线上示例使用
		// console.log('%c uni-app官方团队诚邀优秀前端工程师加盟，一起打造更卓越的uni-app & uniCloud，欢迎投递简历到 <EMAIL>', 'color: red');
		console.log('App Launch');
		// #ifdef APP-PLUS
		// App平台检测升级，服务端代码是通过uniCloud的云函数实现的，详情可参考：https://ext.dcloud.net.cn/plugin?id=4542
		if (plus.runtime.appid !== 'HBuilder') { // 真机运行不需要检查更新，真机运行时appid固定为'HBuilder'，这是调试基座的appid
			checkUpdate()
		}

		// 一键登录预登陆，可以显著提高登录速度
		// uni.preLogin({
		// 	provider: 'univerify',
		// 	success: (res) => {
		// 		// 成功
		// 		this.setUniverifyErrorMsg();
		// 		console.log("preLogin success: ", res);
		// 	},
		// 	fail: (res) => {
		// 		this.setUniverifyLogin(false);
		// 		this.setUniverifyErrorMsg(res.errMsg);
		// 		// 失败
		// 		console.log("preLogin fail res: ", res);
		// 	}
		// })
		// #endif

		uni.getSystemInfo({
			success: function (e) {
				// #ifndef MP
				Vue.prototype.StatusBar = e.statusBarHeight;
				if (e.platform == 'android') {
					Vue.prototype.CustomBar = e.statusBarHeight + 50;
				} else {
					Vue.prototype.CustomBar = e.statusBarHeight + 45;
				};
				// #endif

				// #ifdef MP-WEIXIN
				Vue.prototype.StatusBar = e.statusBarHeight;
				let custom = wx.getMenuButtonBoundingClientRect();
				Vue.prototype.Custom = custom;
				Vue.prototype.CustomBar = custom.bottom + custom.top - e.statusBarHeight + 4;
				// #endif		

				// #ifdef MP-ALIPAY
				Vue.prototype.StatusBar = e.statusBarHeight;
				Vue.prototype.CustomBar = e.statusBarHeight + e.titleBarHeight;
				// #endif
			}
		});
	},
	onShow: function () {
		console.log('App Show');

		// #ifdef H5
		// H5端：动态加载Socket.io
		return new Promise((resolve) => {
			// 如果已经加载过 Socket.io，直接初始化
			if (window.io) {
				this.initSocket();
				resolve();
				return;
			}

			// 动态加载 Socket.io
			const script = document.createElement('script');
			script.src = 'https://cdn.socket.io/4.7.2/socket.io.min.js';
			script.onload = () => {
				console.log('Socket.io 已加载');
				this.initSocket();
				resolve();
			};
			script.onerror = (err) => {
				console.error('加载 Socket.io 失败:', err);
				resolve();
			};
			document.body.appendChild(script);
		});
		// #endif

		// #ifdef APP-PLUS
		// App端：直接初始化WebSocket连接
		this.initSocket();
		// #endif
	},

	onHide: function () {
		console.log('App Hide')
	},
	globalData: {
		test: ''
	},
	methods: {
		//提醒函数
		// #ifdef APP-PLUS
		/* === 本地通知栏 + 震动 + 音效 === */
		showLocalNotification(title, content) {
		  plus.notification.create({
		    title,
		    content,
		    icon: '_www/static/logo.png',   // 可选 48×48 png
		    sound: 'system',                // 系统提示音
		    vibrate: true                   // 震动
		  });
		},
		playLocalSound(file = '_www/static/alert.mp3') {
		  plus.audio.createPlayer(file).play();
		},
		vibrate() {
		  plus.device.vibrate(500);
		},
		triggerAlert(data) {
		  this.showLocalNotification('新消息', data.data || '您有一条新消息');
		  this.vibrate();
		  this.playLocalSound();
		},
		// #endif
		
		
		//
		...mapMutations(['setUniverifyErrorMsg', 'setUniverifyLogin']),

		initSocket() {
			// #ifdef H5
			// H5端使用Socket.io
			if (!window.io) {
				console.error('Socket.io 未加载');
				return;
			}

			// 连接到你的 WebSocket 服务器（替换为你的实际地址）
			const socket = io('http://192.168.1.124:5000', {
			// const socket = io('http://101.43.246.156:5000', {
				transports: ['websocket'], // 强制使用 WebSocket
				reconnection: true,        // 自动重连
				reconnectionAttempts: 5,   // 最大重连次数
				reconnectionDelay: 1000,   // 重连间隔
			});

			// 监听连接成功
			socket.on('connect', () => {
				console.log('WebSocket 连接成功，ID:', socket.id);
			});

			// 监听自定义事件 - H5端
			socket.on('alert', (payload) => {
			  console.log('收到推送消息:', payload);

			  const { message, data } = payload || {};
			  const { id, video_name, event_name } = data || {};

			  // H5端弹窗
			  uni.showModal({
			    title: '新消息',
			    content: message || '您有一条新消息',
			    showCancel: false,
			    confirmText: '去看看',
			    success: (res) => {
			      if (res.confirm) {
			        uni.navigateTo({
			          url: `/pages/views/index/VideoDetail?id=${id}&video_name=${encodeURIComponent(video_name)}&event_name=${encodeURIComponent(event_name)}`
			        });
			      }
			    }
			  });
			});

			// 监听连接错误
			socket.on('connect_error', (err) => {
				console.error('WebSocket 连接错误:', err);
			});

			// 保存 socket 实例到全局（可选）
			this.$socket = socket;
			// #endif

			// #ifdef APP-PLUS
			// App端使用原生WebSocket
			this.initNativeWebSocket();
			// #endif
		},

		// #ifdef APP-PLUS
		// App端原生WebSocket连接
		initNativeWebSocket() {
			const wsUrl = 'ws://192.168.1.124:5000/socket.io/?EIO=4&transport=websocket';
			// const wsUrl = 'ws://101.43.246.156:5000/socket.io/?EIO=4&transport=websocket';

			const socket = new WebSocket(wsUrl);

			socket.onopen = () => {
				console.log('App端 WebSocket 连接成功');
				// Socket.io握手
				socket.send('40'); // Engine.IO握手
			};

			socket.onmessage = (event) => {
				console.log('收到消息:', event.data);

				// 解析Socket.io消息格式
				const data = event.data;
				if (data.startsWith('42')) {
					// Socket.io事件消息格式: 42["event_name", payload]
					try {
						const messageContent = data.substring(2);
						const parsed = JSON.parse(messageContent);

						if (parsed[0] === 'alert' && parsed[1]) {
							const payload = parsed[1];
							const { message, data: eventData } = payload || {};
							const { id, video_name, event_name } = eventData || {};

							// App端系统通知栏 + 震动 + 音效
							this.showLocalNotification('新消息', message || '您有一条新消息');
							this.vibrate();
							this.playLocalSound();

							// 点击通知栏后跳转
							plus.notification.addEventListener('click', () => {
								uni.navigateTo({
									url: `/pages/views/index/VideoDetail?id=${id}&video_name=${encodeURIComponent(video_name)}&event_name=${encodeURIComponent(event_name)}`
								});
							});
						}
					} catch (e) {
						console.error('解析消息失败:', e);
					}
				}
			};

			socket.onerror = (error) => {
				console.error('App端 WebSocket 错误:', error);
			};

			socket.onclose = (event) => {
				console.log('App端 WebSocket 连接关闭:', event);
				// 自动重连
				setTimeout(() => {
					console.log('尝试重新连接...');
					this.initNativeWebSocket();
				}, 3000);
			};

			// 保存socket实例
			this.$socket = socket;
		},
		// #endif
	}
}
</script>

<style lang="scss">
@import "colorui/main.css";
@import "colorui/icon.css";
@import "uview-ui/theme.scss";
@import "uview-ui/index.scss";
// @import '@/uni_modules/uni-scss/index.scss';
/* #ifndef APP-PLUS-NVUE */
/* uni.css - 通用组件、模板样式库，可以当作一套ui库应用 */
@import './common/uni.css';
// @import '@/static/customicons.css';
/* H5 兼容 pc 所需 */
/* #ifdef H5 */
@media screen and (min-width: 768px) {
	body {
		overflow-y: scroll;
	}
}

/* 顶栏通栏样式 */
/* .uni-top-window {
	    left: 0;
	    right: 0;
	} */

uni-page-body {
	background-color: #FFFFFF !important;
	overflow-y: scroll;
	// min-height: 100% !important;
	// height: auto !important;
}

.uni-top-window uni-tabbar .uni-tabbar {
	background-color: #fff !important;
}

.uni-app--showleftwindow .hideOnPc {
	display: none !important;
}

/* #endif */

/* 以下样式用于 hello uni-app 演示所需 */
page {
	background-color: #efeff4;
	height: 100%;
	font-size: 28rpx;
	/* line-height: 1.8; */
}

.fix-pc-padding {
	padding: 0 50px;
}

.uni-header-logo {
	padding: 30rpx;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	margin-top: 10rpx;
}

.uni-header-image {
	width: 100px;
	height: 100px;
}

.uni-hello-text {
	color: #7A7E83;
}

.uni-hello-addfile {
	text-align: center;
	line-height: 300rpx;
	background: #FFF;
	padding: 50rpx;
	margin-top: 10px;
	font-size: 38rpx;
	color: #808080;
}

/* #endif*/
</style>
